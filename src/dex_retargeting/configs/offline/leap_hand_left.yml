retargeting:
  type: position
  urdf_path: leap_hand/leap_hand_left.urdf

  target_joint_names: null
  target_link_names: [ "thumb_tip_head", "index_tip_head", "middle_tip_head", "ring_tip_head", "thumb_dip", "dip", "dip_2", "dip_3" ]

  target_link_human_indices: [ 4, 8, 12, 16, 2, 6, 10, 14 ]
  add_dummy_free_joint: True

  # A smaller alpha means stronger filtering, i.e. more smooth but also larger latency
  # 1 means no filter while 0 means not moving
  low_pass_alpha: 1
