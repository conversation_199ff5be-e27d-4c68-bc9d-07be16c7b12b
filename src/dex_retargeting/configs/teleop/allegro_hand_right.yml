retargeting:
  type: vector
  urdf_path: allegro_hand/allegro_hand_right.urdf

  # Target refers to the retargeting target, which is the robot hand
  target_origin_link_names: [ "wrist", "wrist", "wrist", "wrist" ]
  target_task_link_names: [ "link_15.0_tip", "link_3.0_tip", "link_7.0_tip", "link_11.0_tip" ]
  scaling_factor: 1.6

  # Source refers to the retargeting input, which usually corresponds to the human hand
  # The joint indices of human hand joint which corresponds to each link in the target_link_names
  target_link_human_indices: [ [ 0, 0, 0, 0 ], [ 4, 8, 12, 16 ] ]

  # A smaller alpha means stronger filtering, i.e. more smooth but also larger latency
  low_pass_alpha: 0.2
