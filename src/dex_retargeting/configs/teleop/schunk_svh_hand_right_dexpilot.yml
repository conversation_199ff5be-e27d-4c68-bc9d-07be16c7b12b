retargeting:
  type: Dex<PERSON>ilot
  urdf_path: schunk_hand/schunk_svh_hand_right.urdf

  # Target refers to the retargeting target, which is the robot hand
  target_joint_names: [ 'right_hand_Thumb_Opposition', 'right_hand_Thumb_Flexion', 'right_hand_Index_Finger_Proximal',
                        'right_hand_Index_Finger_Distal', 'right_hand_Finger_Spread', 'right_hand_Pinky',
                        'right_hand_Ring_Finger', 'right_hand_Middle_Finger_Proximal', 'right_hand_Middle_Finger_Distal' ]
  wrist_link_name: "right_hand_base_link"
  finger_tip_link_names: [ "thtip", "fftip", "mftip", "rftip", "lftip" ]
  scaling_factor: 1.2

  # A smaller alpha means stronger filtering, i.e. more smooth but also larger latency
  low_pass_alpha: 0.2
