retargeting:
  type: vector
  urdf_path: inspire_hand/inspire_hand_right.urdf

  # Target refers to the retargeting target, which is the robot hand
  target_joint_names: [ 'pinky_proximal_joint', 'ring_proximal_joint', 'middle_proximal_joint', 'index_proximal_joint',
                        'thumb_proximal_pitch_joint', 'thumb_proximal_yaw_joint' ]
  target_origin_link_names: [ "base", "base", "base", "base", "base" ]
  target_task_link_names: [ "thumb_tip",  "index_tip", "middle_tip", "ring_tip", "pinky_tip" ]
  scaling_factor: 1.15

  # Source refers to the retargeting input, which usually corresponds to the human hand
  # The joint indices of human hand joint which corresponds to each link in the target_link_names
  target_link_human_indices: [ [ 0, 0, 0, 0, 0 ], [ 4, 8, 12, 16, 20 ] ]

  # A smaller alpha means stronger filtering, i.e. more smooth but also larger latency
  low_pass_alpha: 0.2
