name: Core Tests

on:
  push:
    branches: [ main, dev ]
  pull_request:

permissions:
  contents: read

jobs:
  build:

    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [ "3.10" ]

    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          git submodule update --init
          python -m pip install uv
          uv pip install --system torch --index-url https://download.pytorch.org/whl/cpu
          uv pip install --system -e .[dev]
      - name: Run Black
        run: |
          ruff check src/
          ruff format src/ --check
      - name: Test with pytest
        run: |
          pytest