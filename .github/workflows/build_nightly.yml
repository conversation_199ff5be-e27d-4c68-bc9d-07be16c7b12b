name: Build Nightly

on:
  push:
    branches: [ main, dev ]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [ "3.8" ]

    permissions:
      id-token: write
      contents: write

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Build wheels
        run: pip3 install setuptools build --upgrade && python -m build -w

      - name: Upload wheels
        uses: actions/upload-artifact@v4
        with:
          name: wheels
          path: ./dist/*.whl

      - name: Update Nightly Release
        uses: andelf/nightly-release@main
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: nightly
          name: 'Nightly Release'
          prerelease: true
          body: 'Nightly release.'
          files: |
            ./dist/*.whl
