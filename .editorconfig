# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
charset = utf-8
indent_size = 2

[*.{py,java,r,R}]
indent_style = space
indent_size = 4

[*.{xml,xsd,urdf}]
max_line_length = off
indent_style = space
indent_size = 2

[*.{md,Rmd,rst}]
trim_trailing_whitespace = false
indent_style = space
indent_size = 2

